import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Semantic search patterns and intent recognition
const SEARCH_PATTERNS = {
  budget: {
    patterns: ['cheap', 'affordable', 'budget', 'under', 'less than', 'below'],
    intent: 'price_filter'
  },
  luxury: {
    patterns: ['luxury', 'premium', 'high-end', 'expensive', 'top-tier'],
    intent: 'luxury_filter'
  },
  range: {
    patterns: ['long range', 'far', 'distance', 'miles', 'range', 'travel'],
    intent: 'range_filter'
  },
  family: {
    patterns: ['family', 'kids', 'children', 'spacious', 'large', 'suv', 'seats'],
    intent: 'family_filter'
  },
  performance: {
    patterns: ['fast', 'quick', 'acceleration', 'sporty', 'performance'],
    intent: 'performance_filter'
  },
  efficiency: {
    patterns: ['efficient', 'economy', 'mpge', 'energy', 'eco'],
    intent: 'efficiency_filter'
  },
  charging: {
    patterns: ['charging', 'fast charge', 'supercharger', 'dc', 'quick charge'],
    intent: 'charging_filter'
  },
  commute: {
    patterns: ['commute', 'daily', 'work', 'city', 'urban'],
    intent: 'commute_filter'
  }
}

const FEATURE_KEYWORDS = [
  'autopilot', 'self-driving', 'autonomous',
  'all-wheel drive', 'awd', '4wd',
  'sunroof', 'panoramic roof',
  'leather seats', 'heated seats',
  'premium audio', 'sound system',
  'navigation', 'gps',
  'wireless charging', 'qi charging',
  'heads-up display', 'hud',
  'adaptive cruise control', 'acc'
]

function analyzeSearchIntent(query: string): {
  intent: string[]
  extractedValues: Record<string, any>
  semanticSuggestions: Array<{ text: string; description: string }>
} {
  const lowerQuery = query.toLowerCase()
  const intents: string[] = []
  const extractedValues: Record<string, any> = {}
  const semanticSuggestions: Array<{ text: string; description: string }> = []

  // Analyze patterns
  Object.entries(SEARCH_PATTERNS).forEach(([category, config]) => {
    if (config.patterns.some(pattern => lowerQuery.includes(pattern))) {
      intents.push(config.intent)
      
      // Generate semantic suggestions based on intent
      switch (config.intent) {
        case 'price_filter':
          semanticSuggestions.push({
            text: 'EVs under $40,000',
            description: 'Affordable electric vehicles'
          })
          semanticSuggestions.push({
            text: 'Best value electric cars',
            description: 'High value-for-money EVs'
          })
          break
        case 'range_filter':
          semanticSuggestions.push({
            text: 'Long-range EVs (300+ miles)',
            description: 'Electric vehicles with extended range'
          })
          break
        case 'family_filter':
          semanticSuggestions.push({
            text: 'Family-friendly electric SUVs',
            description: 'Spacious EVs for families'
          })
          break
        case 'performance_filter':
          semanticSuggestions.push({
            text: 'High-performance electric cars',
            description: 'Fast acceleration and sporty EVs'
          })
          break
      }
    }
  })

  // Extract price ranges
  const priceMatch = lowerQuery.match(/(?:under|below|less than)\s*\$?(\d+)(?:k|,000)?/i)
  if (priceMatch) {
    let price = parseInt(priceMatch[1])
    if (lowerQuery.includes('k') || price < 1000) {
      price *= 1000
    }
    extractedValues.maxPrice = price
  }

  // Extract range requirements
  const rangeMatch = lowerQuery.match(/(\d+)\s*(?:\+)?\s*miles?/i)
  if (rangeMatch) {
    extractedValues.minRange = parseInt(rangeMatch[1])
  }

  return { intents, extractedValues, semanticSuggestions }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = parseInt(searchParams.get('limit') || '8')

    if (!query || query.length < 2) {
      return NextResponse.json({
        modelSuggestions: [],
        semanticSuggestions: [],
        featureSuggestions: [],
        query: query || ''
      })
    }

    // Analyze search intent
    const { intents, extractedValues, semanticSuggestions } = analyzeSearchIntent(query)

    // Build intelligent query based on intent
    let evQuery = supabase
      .from('ev_models')
      .select(`
        id,
        make,
        model,
        year,
        trim,
        body_type,
        price_msrp,
        range_epa_miles,
        efficiency_mpge,
        acceleration_0_60_mph,
        charging_speed_dc_kw,
        images,
        is_featured,
        best_value,
        popularity_score,
        ev_manufacturers!inner(name, logo_url)
      `)
      .eq('production_status', 'current')

    // Apply intent-based filters
    if (extractedValues.maxPrice) {
      evQuery = evQuery.lte('price_msrp', extractedValues.maxPrice * 100) // Convert to cents
    }

    if (extractedValues.minRange) {
      evQuery = evQuery.gte('range_epa_miles', extractedValues.minRange)
    }

    if (intents.includes('family_filter')) {
      evQuery = evQuery.eq('body_type', 'suv')
    }

    if (intents.includes('luxury_filter')) {
      evQuery = evQuery.gte('price_msrp', 7000000) // $70k+
    }

    if (intents.includes('budget_filter')) {
      evQuery = evQuery.lte('price_msrp', 4000000) // Under $40k
    }

    // Text search for make/model
    const textSearchQuery = query.replace(/\b(?:under|below|less than|over|above|more than)\s*\$?\d+(?:k|,000)?\b/gi, '').trim()
    if (textSearchQuery) {
      evQuery = evQuery.or(`make.ilike.%${textSearchQuery}%,model.ilike.%${textSearchQuery}%,trim.ilike.%${textSearchQuery}%`)
    }

    const { data: modelResults, error } = await evQuery
      .order('popularity_score', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Intelligent search error:', error)
      return NextResponse.json({
        modelSuggestions: [],
        semanticSuggestions: [],
        featureSuggestions: [],
        query
      })
    }

    // Generate feature suggestions based on query
    const featureSuggestions = FEATURE_KEYWORDS
      .filter(feature => feature.toLowerCase().includes(query.toLowerCase()) || 
                        query.toLowerCase().includes(feature.toLowerCase()))
      .slice(0, 3)

    // Enhanced semantic suggestions based on popular searches and trends
    const enhancedSemanticSuggestions = [...semanticSuggestions]
    
    if (query.toLowerCase().includes('tesla')) {
      enhancedSemanticSuggestions.push({
        text: 'Tesla alternatives',
        description: 'Similar EVs to Tesla models'
      })
    }

    if (query.toLowerCase().includes('first') || query.toLowerCase().includes('new')) {
      enhancedSemanticSuggestions.push({
        text: 'Best first electric car',
        description: 'Ideal EVs for first-time buyers'
      })
    }

    return NextResponse.json({
      modelSuggestions: modelResults?.slice(0, 4) || [],
      semanticSuggestions: enhancedSemanticSuggestions.slice(0, 3),
      featureSuggestions,
      intents,
      extractedValues,
      query
    })

  } catch (error) {
    console.error('Intelligent search API error:', error)
    return NextResponse.json(
      { error: 'Failed to perform intelligent search' },
      { status: 500 }
    )
  }
}
